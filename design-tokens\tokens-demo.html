<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Material 3 Design Tokens v2 Demo</title>
    <link rel="stylesheet" href="tokens.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .toggle-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .color-card {
            border-radius: 12px;
            padding: 20px;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .color-name {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .color-value {
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .typography-demo {
            margin: 40px 0;
        }

        .typography-sample {
            margin: 20px 0;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid var(--md-sys-color-outline);
        }

        .section-title {
            border-bottom: 2px solid var(--md-sys-color-primary);
            padding-bottom: 8px;
            margin-bottom: 24px;
        }

        .responsive-note {
            padding: 16px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid var(--md-sys-color-primary);
        }
    </style>
</head>
<body class="light-theme md-surface">
    <div class="theme-toggle">
        <button class="toggle-btn md-primary" onclick="toggleTheme()">
            🌙 切換深色主題
        </button>
    </div>

    <div class="demo-container">
        <header>
            <h1 class="md-headline-large section-title">Material 3 Design Tokens v2</h1>
            <p class="md-body-large text-on-surface-variant">
                基於 Material 3 設計系統的完整 Design Tokens 實作
            </p>
        </header>

        <section class="typography-demo">
            <h2 class="md-headline-medium section-title">字體排版系統</h2>
            
            <div class="typography-sample">
                <h1 class="md-headline-large">Headline Large - 主標題</h1>
                <h2 class="md-headline-medium">Headline Medium - 次標題</h2>
                <h3 class="md-headline-small">Headline Small - 小標題</h3>
                <h4 class="md-title-large">Title Large - 大標題</h4>
                <h5 class="md-title-medium">Title Medium - 中標題</h5>
                <h6 class="md-title-small">Title Small - 小標題</h6>
                <p class="md-body-large">Body Large - 大內文，適合重要段落</p>
                <p class="md-body-medium">Body Medium - 標準內文，最常用的文字大小</p>
                <p class="md-body-small">Body Small - 小內文，適合輔助說明</p>
                <span class="md-label-small">Label Small - 標籤文字</span>
            </div>

            <div class="responsive-note md-surface">
                <strong>📱 響應式設計：</strong>
                在手機螢幕 (≤768px) 上，字體大小會自動調整為更適合的尺寸。
            </div>
        </section>

        <section>
            <h2 class="md-headline-medium section-title">顏色系統</h2>
            
            <h3 class="md-title-large">系統顏色</h3>
            <div class="color-palette">
                <div class="color-card md-primary">
                    <div class="color-name">Primary</div>
                    <div class="color-value">主要顏色</div>
                </div>
                <div class="color-card md-secondary">
                    <div class="color-name">Secondary</div>
                    <div class="color-value">次要顏色</div>
                </div>
                <div class="color-card md-surface">
                    <div class="color-name">Surface</div>
                    <div class="color-value">表面顏色</div>
                </div>
                <div class="color-card md-error">
                    <div class="color-name">Error</div>
                    <div class="color-value">錯誤顏色</div>
                </div>
            </div>

            <h3 class="md-title-large">文字顏色範例</h3>
            <div class="typography-sample">
                <p class="text-primary">主要顏色文字 - Primary Color Text</p>
                <p class="text-on-surface">表面文字 - On Surface Text</p>
                <p class="text-on-surface-variant">變體文字 - On Surface Variant</p>
                <p class="text-error">錯誤文字 - Error Text</p>
            </div>
        </section>

        <section>
            <h2 class="md-headline-medium section-title">使用方式</h2>
            <div class="typography-sample">
                <h3 class="md-title-medium">1. 引入 CSS 檔案</h3>
                <pre class="md-body-small"><code>&lt;link rel="stylesheet" href="tokens2.css"&gt;</code></pre>

                <h3 class="md-title-medium">2. 設定主題</h3>
                <pre class="md-body-small"><code>&lt;body class="light-theme"&gt; 或 &lt;body class="dark-theme"&gt;</code></pre>

                <h3 class="md-title-medium">3. 使用 CSS 變數</h3>
                <pre class="md-body-small"><code>color: var(--md-sys-color-primary);
background: var(--md-sys-color-surface);</code></pre>

                <h3 class="md-title-medium">4. 使用輔助類別</h3>
                <pre class="md-body-small"><code>&lt;h1 class="md-headline-large"&gt;標題&lt;/h1&gt;
&lt;div class="md-primary"&gt;主要顏色區塊&lt;/div&gt;</code></pre>
            </div>
        </section>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const btn = document.querySelector('.toggle-btn');
            
            if (body.classList.contains('light-theme')) {
                body.classList.remove('light-theme');
                body.classList.add('dark-theme');
                btn.textContent = '☀️ 切換明亮主題';
            } else {
                body.classList.remove('dark-theme');
                body.classList.add('light-theme');
                btn.textContent = '🌙 切換深色主題';
            }
        }
    </script>
</body>
</html>

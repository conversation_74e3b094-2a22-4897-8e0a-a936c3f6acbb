/**
 * STT-SaaS 組件樣式檔案
 * 基於 Material 3 Design Tokens 重構
 * 版本: 2.0.0
 * 建立日期: 2025-01-09
 *
 * 依賴: tokens.css (Material 3 Design Tokens)
 * 支援: 明暗主題切換、響應式設計
 */

/* ========================================
   引入 Material 3 Design Tokens
   ======================================== */
@import url('./tokens.css');

/* ========================================
   全域基礎設定
   ======================================== */

/* 字體載入優化 */
@font-face {
  font-family: 'Noto Sans';
  src: url('https://fonts.googleapis.com/css2?family=Noto+Sans:wght@100;200;300;400;500;600;700;800;900&display=swap');
  font-display: swap;
}

/* 全域重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  /* 確保 1rem = 16px */
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Noto Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
  background-color: var(--md-sys-color-surface);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* ========================================
   通用工具類別
   ======================================== */

/* 文字工具類別 */
.text-primary {
  color: var(--md-sys-color-primary);
}

.text-secondary {
  color: var(--md-sys-color-on-surface-variant);
}

.text-tertiary {
  color: var(--md-ref-palette-neutral-500);
}

.text-placeholder {
  color: var(--md-ref-palette-neutral-400);
}

/* 背景工具類別 */
.bg-primary {
  background-color: var(--md-sys-color-surface);
}

.bg-secondary {
  background-color: var(--md-sys-color-surface-container);
}

.bg-subtle {
  background-color: var(--md-ref-palette-neutral-50);
}

/* 邊框工具類別 */
.border-primary {
  border-color: var(--md-sys-color-outline);
}

.border-secondary {
  border-color: var(--md-ref-palette-neutral-200);
}

.border-accent {
  border-color: var(--md-sys-color-primary);
}

/* 字重工具類別 */
.font-thin {
  font-weight: 100;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-black {
  font-weight: 900;
}

/* ========================================
   常用元件樣式
   ======================================== */

/* 按鈕基礎樣式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: auto;
  min-height: 40px;
  padding: 7px 16px;
  border: none;
  border-radius: 60px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 500;
  line-height: 1.5;
  text-decoration: none;
  user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 按鈕變體 */
.btn-primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--md-ref-palette-primary-700);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px var(--md-sys-color-surface-dim);
}

.btn-secondary {
  background-color: var(--md-sys-color-secondary);
  color: var(--md-sys-color-on-secondary);
  padding: 11px;
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--md-ref-palette-neutral-300);
}

/* 按鈕尺寸 */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: var(--md-sys-typescale-body-small-size);
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: var(--md-sys-typescale-body-large-size);
}

/* 輸入框樣式 */
.input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 4px;
  background-color: var(--md-sys-color-surface-container);
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
  transition: border-color 0.2s ease, background-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-surface);
}

.input::placeholder {
  color: var(--md-sys-color-on-surface-variant);
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
}

/* 表單控制項樣式 (與 input 一致) */
.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 4px;
  background-color: var(--md-sys-color-surface-container);
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
  transition: border-color 0.2s ease, background-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-surface);
}

/* 搜尋輸入容器 */
.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  z-index: 1;
  color: var(--md-sys-color-on-surface-variant);
  pointer-events: none;
}

.search-select {
  padding-left: 2.5rem;
  /* 為搜尋圖示留出空間 */
  appearance: none;
  /* 移除預設下拉箭頭 */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23888888' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* 選擇的語言標籤容器 */
.selected-languages {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  color: var(--md-sys-color-on-surface);
  margin: 1rem 0;
}

/* 語言標籤樣式 */
.language-pill {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: var(--md-sys-color-surface-container);
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 20px;
  font-size: var(--md-sys-typescale-body-small-size);
  font-weight: 400;
  color: var(--md-sys-color-on-surface);
  transition: all 0.2s ease;
}

.language-pill:hover {
  background-color: var(--md-ref-palette-primary-100);
  border-color: var(--md-sys-color-primary);
}

/* 移除按鈕樣式 */
.remove-pill {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  background: none;
  border: none;
  border-radius: 50%;
  color: var(--md-sys-color-on-surface-variant);
  cursor: pointer;
  font-size: 1rem;
  line-height: 1;
  transition: all 0.2s ease;
}

.remove-pill:hover {
  background-color: var(--md-sys-color-error);
  color: var(--md-sys-color-on-error);
}

/* 表單樣式 */
form .btn{
  height: 48px;
  width: 100%;
}

#login-form, #language-select-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: center;
}

.form-group h2 {
  color: var(--md-sys-color-on-surface);
  margin: 0;
  font-size: var(--md-sys-typescale-title-medium-size);
  font-weight: 500;
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 6px;
  background-color: var(--md-sys-color-surface-container);
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface);
  transition: border-color 0.2s ease, background-color 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-surface);
}

.form-group input::placeholder {
  color: var(--md-sys-color-on-surface-variant);
}

.label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 500;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
}

/* 卡片樣式 */
.card {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: center;
  width: 100%;
  max-width: 400px;
  max-height: 600px;
  padding: 1rem;
  background-color: var(--md-sys-color-surface-container);
  border-radius: 16px;
  box-shadow: 0 4px 6px var(--md-sys-color-surface-dim);
  border: 1px solid var(--md-sys-color-outline);
  transition: box-shadow 0.2s ease, background-color 0.2s ease;
}

.card:hover {
  box-shadow: 0 8px 16px var(--md-sys-color-surface-dim);
}

.card-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--md-sys-color-outline);
}

.card-title {
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-title-small-size);
  font-weight: 500;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
  margin: 0;
  text-align: center;
}

.card-content {
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface-variant);
}

/* 連結樣式 */
.link {
  color: var(--md-sys-color-primary);
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 500;
  line-height: 1.5;
  text-decoration: none;
  transition: color 0.2s ease;
}

.link:hover {
  color: var(--md-ref-palette-primary-700);
  text-decoration: underline;
}

/* 狀態訊息 */
.message {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  border: 1px solid transparent;
}

.message-success {
  background-color: var(--md-ref-palette-accent-100);
  color: var(--md-ref-palette-accent-800);
  border-color: var(--md-ref-palette-accent-300);
}

.message-warning {
  background-color: #fef3c7;
  color: #92400e;
  border-color: #fcd34d;
}

.message-error {
  background-color: var(--md-ref-palette-error-100);
  color: var(--md-sys-color-error);
  border-color: var(--md-ref-palette-error-300);
}

.message-info {
  background-color: var(--md-ref-palette-primary-100);
  color: var(--md-ref-palette-primary-800);
  border-color: var(--md-ref-palette-primary-300);
}

/* ========================================
   響應式工具
   ======================================== */

/* 隱藏/顯示工具 */
.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

/* 響應式顯示 */
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }

  .visible-mobile {
    display: block !important;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }

  .visible-desktop {
    display: block !important;
  }
}

/* ========================================
   主題切換增強
   ======================================== */

/* 主題切換按鈕樣式 */
.theme-toggle-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 12px;
  border: none;
  border-radius: 50%;
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  cursor: pointer;
  font-size: 1.25rem;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px var(--md-sys-color-surface-dim);
}

.theme-toggle-btn:hover {
  background-color: var(--md-ref-palette-primary-700);
  transform: scale(1.1);
}

/* 暗色主題特定樣式調整 */
.dark-theme .card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.dark-theme .card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
}

.dark-theme .btn-primary:hover:not(:disabled) {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* ========================================
   可訪問性增強
   ======================================== */

/* 焦點樣式 */
*:focus {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
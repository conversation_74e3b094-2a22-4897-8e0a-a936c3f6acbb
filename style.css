/* ========================================
   STT-SaaS 主要樣式檔案
   統一引入設計權杖系統
   ======================================== */

/* 引入統一設計權杖檔案 */
@import url('./design-tokens/tokens.css');

/* ========================================
   專案特定樣式
   使用設計權杖系統
   ======================================== */

/* 主要容器 */
main {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: rgb(0, 0, 0, 25%);
}

/* 翻譯頁面主容器 */
main.translate-main {
    margin: 0 auto;
    padding: 0 32px;
    background-color: var(--md-sys-color-surface);
    justify-content: flex-start;
}

/* 應用程式標題 */
.app-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
}

.translate-main .app-header {
    margin-bottom: 0;
}

/* ========================================
   翻譯頁面專用樣式
   ======================================== */

/* 控制按鈕區 */
.control-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 2rem;
}

.control-buttons .btn .material-symbols-outlined {
    font-size: 1.2rem;
    padding-right: 0.5rem;
}

/* 語言標籤區 */
.tool-bar {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.language-tags {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px;
    border-radius: 60px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 翻譯顯示區域 */
.translation-display-area {
    max-width: 960px;
    margin-bottom: 2rem;
    flex: 1;
}

.translation-flex {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    gap: 2rem;
    height: 100%;
}

.translation-column {
    width: 25%;
    transition: all 0.2s ease;
}

.translation-column:hover {
    transform: translateY(-2px);
}

.translation-content {
    font-family: 'Noto Sans', 'Segoe UI', sans-serif;
    font-size: var(--md-sys-typescale-body-medium-size);
    line-height: 1.5;
    color: var(--md-sys-color-on-surface);
}

.translation-content p {
    margin-bottom: 1rem;
}

.translation-content p:last-child {
    margin-bottom: 0;
}

.translation-content strong {
    font-weight: 600;
    color: var(--md-sys-color-on-surface);
}

/* 即時字幕區域 */
.live-subtitles {
    height: 180px;
    max-width: 720px;
    margin: 0 auto 2rem;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.subtitle-title {
    font-family: 'Noto Sans', 'Segoe UI', sans-serif;
    font-size: var(--md-sys-typescale-title-small-size);
    font-weight: 500;
    color: var(--md-sys-color-on-surface);
    margin: 0 0 1rem 0;
    border-bottom: 2px solid var(--md-sys-color-primary);
    padding-bottom: 0.5rem;
}

.subtitle-content {
    flex: 1;
    overflow-y: auto;
    font-family: 'Noto Sans', 'Segoe UI', sans-serif;
    font-size: var(--md-sys-typescale-body-medium-size);
    line-height: 1.5;
    color: var(--md-sys-color-on-surface);
}

.subtitle-content p {
    margin: 0;
}

/* QR Code 浮動按鈕 */
.qr-code-btn {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.qr-code-btn:hover {
    background: var(--md-ref-palette-primary-700);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.qr-code-btn .material-symbols-outlined {
    font-size: 1.5rem;
}

/* 動畫效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.translation-column {
    animation: fadeInUp 0.6s ease-out;
}

.translation-column:nth-child(1) {
    animation-delay: 0.1s;
}

.translation-column:nth-child(2) {
    animation-delay: 0.2s;
}

.translation-column:nth-child(3) {
    animation-delay: 0.3s;
}

.translation-column:nth-child(4) {
    animation-delay: 0.4s;
}

/* .language-tag.active {
    animation: pulse 2s infinite;
} */

.qr-code-btn:active {
    transform: scale(0.95);
}

/* ========================================
   QR Code Card 組件樣式
   基於圖片標記的間距規則實現
   ======================================== */

/* QR Code 卡片遮罩層 */
.qr-code-card-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: 1rem; /* 1rem 外邊距，對應圖片標記 "1" */
    box-sizing: border-box;
}

/* QR Code 卡片主體 */
.qr-code-card {
    background-color: var(--md-sys-color-surface-container);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: qrCardSlideIn 0.3s ease-out;
}

/* 卡片標題區域 */
.qr-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1rem 0 1rem; /* 頂部 1rem，左右 1rem，對應圖片標記 "1" */
    margin-bottom: 1rem; /* 1rem 下邊距，對應圖片標記 "1" */
}

.qr-card-title {
    font-family: var(--md-sys-typescale-title-medium-family);
    font-size: var(--md-title-small-size);
    font-weight: 700;
    color: var(--md-sys-color-on-surface);
    margin: auto;
}

.qr-card-close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem; /* 2rem 寬度，對應圖片標記 "2" 的比例 */
    height: 2rem; /* 2rem 高度，對應圖片標記 "2" 的比例 */
    border: none;
    border-radius: 50%;
    background-color: transparent;
    color: var(--md-sys-color-on-surface-variant);
    cursor: pointer;
    transition: all 0.2s ease;
}

.qr-card-close-btn:hover {
    background-color: var(--md-sys-color-hover);
    color: var(--md-sys-color-on-surface);
}

.qr-card-close-btn:focus {
    outline: 2px solid var(--md-sys-color-primary);
    outline-offset: 2px;
}

/* QR Code 內容區域 */
.qr-card-content {
    padding: 0 2rem 2rem 2rem; /* 左右 2rem，底部 2rem，對應圖片標記 "2" */
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem; /* 2rem 間距，對應圖片標記 "2" */
}

/* QR Code 圖片容器 */
.qr-code-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.qr-code-image {
    width: 200px;
    height: 200px;
    border: 4px solid var(--md-sys-color-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--md-sys-color-surface);
    position: relative;
}

.qr-code-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.qr-placeholder-icon {
    font-size: 4rem;
    color: var(--md-sys-color-on-surface-variant);
}

/* SCAN ME 按鈕容器 */
.scan-button-container {
    display: flex;
    justify-content: center;
    width: 100%;
}

.scan-me-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem; /* 1rem 上下，2rem 左右，對應圖片標記 */
    background-color: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    border: none;
    border-radius: 60px;
    font-family: 'Noto Sans', 'Segoe UI', sans-serif;
    font-size: var(--md-sys-typescale-body-large-size);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 160px;
}

.scan-me-btn:hover {
    background-color: var(--md-ref-palette-primary-700);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.scan-me-btn:focus {
    outline: 2px solid var(--md-sys-color-primary);
    outline-offset: 2px;
}

.scan-icon {
    font-size: 1.25rem;
}

.scan-text {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* 網址顯示區域 */
.url-display-container {
    width: 100%;
    display: flex;
    justify-content: center;
}

.url-display {
    background-color: var(--md-sys-color-surface);
    border: 1px solid var(--md-sys-color-outline);
    border-radius: 8px;
    padding: 1rem; /* 1rem 內邊距，對應圖片標記 "1" */
    width: 100%;
    max-width: 300px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.url-text {
    font-family: 'Noto Sans', 'Segoe UI', sans-serif;
    font-size: var(--md-sys-typescale-body-medium-size);
    color: var(--md-sys-color-on-surface);
    word-break: break-all;
    line-height: 1.4;
}

/* QR Code 卡片動畫 */
@keyframes qrCardSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* ========================================
   響應式設計
   ======================================== */
/* 平板、電腦 Landscape (> 1024px) */
@media (min-width: 1024px) {
    section {
        max-width: 960px;
        margin: 0 auto;
        /* 容器內距 (Gutter) 保持 16px */
    }
}

/* 平板 Portrait (744-1023px) */
@media (min-width: 744px) {
    section {
        margin: 0 32px;
    }

    main.bg-primary {
        padding: 0 1rem;
    }

    .translation-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .control-buttons .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .language-tags {
        flex-wrap: wrap;
        justify-content: center;
    }

    .live-subtitles {
        height: 160px;
        padding: 1rem;
    }

    .qr-code-btn {
        bottom: 1.5rem;
        right: 1.5rem;
        width: 48px;
        height: 48px;
    }

    .qr-code-btn .material-symbols-outlined {
        font-size: 1.3rem;
    }
}

/* 手機 Landscape (600-900px) */
@media (min-width: 600px) and (max-width: 900px) {
    section {
        width: 100%;
    }
}

/* 手機 Portrait: 320-600px */
@media (min-width: 320px) and (max-width: 600px) {
    section {
        width: 100%;
        padding: 16px;
        margin: 0 16px;
        box-sizing: border-box;
    }

    main {
        padding: 1rem;
    }

    main.bg-primary {
        padding: 0 0.5rem;
    }

    .app-header {
        margin-bottom: 1rem;
    }

    /* .app-header h1 {
        font-size: var(--md-sys-typescale-headline-small-size);
    } */

    .control-buttons {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .control-buttons .btn {
        width: 100%;
        justify-content: center;
    }

    .translation-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .translation-column {
        padding: 1rem;
    }

    .language-tags {
        padding: 0.25rem;
        gap: 0.25rem;
    }

    .language-tag {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .live-subtitles {
        height: 140px;
        padding: 1rem;
        margin: 0 0.5rem;
    }

    .subtitle-title {
        font-size: var(--md-sys-typescale-title-medium-size);
        margin-bottom: 0.75rem;
    }

    .qr-code-btn {
        bottom: 1rem;
        right: 1rem;
        width: 44px;
        height: 44px;
    }

    .qr-code-btn .material-symbols-outlined {
        font-size: 1.2rem;
    }

    .entry-form-container {
        padding: 1.5rem;
    }
}

/* QR Code 卡片響應式設計 */
@media (min-width: 744px) {
    .qr-code-card {
        max-width: 400px;
    }

    .qr-code-image {
        width: 200px;
        height: 200px;
    }
}

@media (min-width: 320px) and (max-width: 600px) {
    .qr-code-card-overlay {
        padding: 0.5rem; /* 手機上減少外邊距 */
    }

    .qr-code-card {
        max-width: 100%;
        margin: 0;
    }

    .qr-card-header {
        padding: 0.75rem 1rem 0 1rem;
        margin-bottom: 0.75rem;
    }

    .qr-card-title {
        font-size: var(--md-sys-typescale-title-small-size);
    }

    .qr-card-content {
        padding: 0 1rem 1.5rem 1rem; /* 手機上減少內邊距 */
        gap: 1.5rem; /* 手機上減少間距 */
    }

    .qr-code-image {
        width: 160px;
        height: 160px;
    }

    .qr-placeholder-icon {
        font-size: 3rem;
    }

    .scan-me-btn {
        padding: 0.75rem 1.5rem;
        font-size: var(--md-sys-typescale-body-medium-size);
        min-width: 140px;
    }

    .scan-icon {
        font-size: 1.1rem;
    }

    .url-display {
        padding: 0.75rem;
        max-width: 100%;
    }

    .url-text {
        font-size: var(--md-sys-typescale-body-small-size);
    }
}

/* 大螢幕優化 */
@media (min-width: 1200px) {
    .translation-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .qr-code-card {
        max-width: 450px;
    }

    .qr-code-image {
        width: 220px;
        height: 220px;
    }
}